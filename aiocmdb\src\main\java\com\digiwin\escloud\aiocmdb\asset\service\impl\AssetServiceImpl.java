package com.digiwin.escloud.aiocmdb.asset.service.impl;

import com.digiwin.escloud.aiocmdb.asset.dao.AssetCategoryMapper;
import com.digiwin.escloud.aiocmdb.asset.model.AssetCategory;
import com.digiwin.escloud.aiocmdb.asset.model.AssetLevelBigDataParam;
import com.digiwin.escloud.aiocmdb.asset.model.AssetSaveBigDataParam;
import com.digiwin.escloud.aiocmdb.asset.model.CmdbModelDataFieldRelationMapping;
import com.digiwin.escloud.aiocmdb.asset.service.AssetService;
import com.digiwin.escloud.aiocmdb.asset.service.IAssetCategoryService;
import com.digiwin.escloud.aiocmdb.assetmaintenance.bigdata.BigDataUtil;
import com.digiwin.escloud.aiocmdb.assetmaintenancev2.model.AssetAttribute;
import com.digiwin.escloud.aiocmdb.assetmaintenancev2.service.AssetRelatedMapService;
import com.digiwin.escloud.aiocmdb.etl.dao.EtlMapper;
import com.digiwin.escloud.aiocmdb.etl.model.EtlModelField;
import com.digiwin.escloud.aiocmdb.maintenancerecord.service.MrService;
import com.digiwin.escloud.aiocmdb.model.dao.ModelMapper;
import com.digiwin.escloud.aiocmdb.util.CommonUtils;
import com.digiwin.escloud.aioitms.model.authorize.AiopsAuthStatus;
import com.digiwin.escloud.aioitms.model.bigdata.StarRocksEntity;
import com.digiwin.escloud.aioitms.model.device.AiopsKitDevice;
import com.digiwin.escloud.aioitms.model.instance.AiopsItemContextDTO;
import com.digiwin.escloud.aioitms.model.instance.MergeStatisticsDetailInfo;
import com.digiwin.escloud.aioitms.model.instance.MergeStatisticsDetailRequest;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.feign.AioItmsFeignClient;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.DateUtil;
import com.digiwin.escloud.common.util.LongUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import com.digiwin.escloud.etl.model.EtlEngine;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.digiwin.escloud.common.util.DateUtil.DATE_TIME_FORMATTER;

@Slf4j
@Service
public class AssetServiceImpl implements AssetService, ParamCheckHelp {
    private static final String ASSET_STATUS = "status";
    private static final String ASSET_STATUS_INVALID = "INVALID";
    private static final String ASSET_AI_ID = "aiId";
    protected static final List<String> INSTANCE_STATUS_LIST = Lists.newArrayList(
            AiopsAuthStatus.AUTHED.getCode(),
            AiopsAuthStatus.UNAUTH.getCode(),
            AiopsAuthStatus.NONE.getCode());

    private static final String STATUS_ONLINE = "Online";
    private static final String SERVICECLOUD_MODEL_DB = "servicecloud";
    private static final String STATUS_OFFLINE = "Offline";
    private static final String STATUS_NOT_INSTALLED = "NotInstalled";
    private static final String USE_STATUS_IN_USE = "InUse";
    private static final String MANAGER_STATUS_MAINTENANCE_REQUIRED = "MaintenanceRequired";


    @Autowired
    private BigDataUtil bigDataUtil;

    @Autowired
    private EtlMapper etlMapper;

    @Autowired
    private MrService mrService;

    @Autowired
    private AssetCategoryMapper assetCategoryMapper;

    @Autowired
    private CommonUtils commonUtils;
    @Autowired
    private ModelMapper modelMapper;

    @Autowired
    private AssetRelatedMapService assetRelatedMapService;

    @Autowired
    private IAssetCategoryService assetCategoryService;

    @Autowired
    private AssetAutomaticallyEstablishedService assetAutomaticallyEstablishedService;
    @Autowired
    private AioItmsFeignClient aioItmsFeignClient;

    @Override
    public BaseResponse deleteAsset(String modelCode, Long id, Long eid) {
        Optional<BaseResponse> optResponse = checkParamIsEmpty(id, "id");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        Map<String, Object> map = new HashMap<>();
        map.put("modelCode", modelCode);
        map.put("sinkType", "starrocks");
        EtlEngine mainEtlEngine = etlMapper.getMainEtlEngine(map);
        String sinkPk = mainEtlEngine.getSinkPk();
        if (StringUtils.isEmpty(sinkPk) || (StringUtils.isNotEmpty(sinkPk) && sinkPk.contains(","))) {
            return BaseResponse.error("1", "not support pk type");
        }
        String deleteSql = "delete from " + SERVICECLOUD_MODEL_DB + modelCode + " where " + sinkPk + " = " + id;
        bigDataUtil.srSave(deleteSql);
        mrService.removeMrDetailByIdListAndEid(modelCode, Stream.of(id).map(Object::toString).collect(Collectors.toList()),eid);

        return BaseResponse.ok();

    }

    /**
     * 暂时用不到
     * @param aicList
     * @return
     */
    @Override
    public BaseResponse invalidCmdbAsset(List<AiopsItemContextDTO> aicList) {
        if (CollectionUtils.isEmpty(aicList)) {
            log.info("[invalidCmdbAsset] Input list is empty, nothing to process.");
            return BaseResponse.ok();
        }

        Map<String, List<AiopsItemContextDTO>> aiopsItemMap = aicList.stream()
                .collect(Collectors.groupingBy(AiopsItemContextDTO::getAiopsItem));

        List<AssetCategory> assetCategoryList = assetCategoryMapper.selectAssetCategorySinkNameByAiopsItemList(aiopsItemMap.keySet());

        if (CollectionUtils.isEmpty(assetCategoryList)) {
            log.warn("[invalidCmdbAsset] No matching asset categories found for the given aiopsItems: {}", aiopsItemMap.keySet());
            return BaseResponse.ok();
        }

        for (AssetCategory assetCategory : assetCategoryList) {
            try {
                String modelCode = assetCategory.getModelCode();
                List<AiopsItemContextDTO> dtoList = aiopsItemMap.get(assetCategory.getAiopsItem());

                if (CollectionUtils.isEmpty(dtoList)) {
                    continue;
                }
                List<Long> aiIdList = dtoList.stream().map(AiopsItemContextDTO::getAiId).collect(Collectors.toList());
                if (aiIdList.isEmpty()) {
                    continue;
                }

                String idsAsString = aiIdList.stream().map(String::valueOf).collect(Collectors.joining(","));
                String selectSql = String.format("SELECT assetId, aiId FROM %s.%s WHERE %s IN (%s)",
                        SERVICECLOUD_MODEL_DB,
                        modelCode,
                        ASSET_AI_ID,
                        idsAsString);

                log.debug("[invalidCmdbAsset] Executing StarRocks query for modelCode [{}]: {}", modelCode, selectSql);
                List<Map<String, Object>> maps = bigDataUtil.starrocksQuery(selectSql);

                if (CollectionUtils.isEmpty(maps)) {
                    log.warn("[invalidCmdbAsset] No assets found in StarRocks for modelCode [{}] with aiIds: {}", modelCode, idsAsString);
                    continue;
                }

                List<LinkedHashMap<String, Object>> finalDataList = maps.stream().map(map -> {
                    LinkedHashMap<String, Object> map1 = new LinkedHashMap<>(map);
                    map1.put(ASSET_STATUS, ASSET_STATUS_INVALID);
                    return map1;
                }).collect(Collectors.toList());

                log.info("[invalidCmdbAsset] Updating {} assets to invalid status for modelCode [{}].", finalDataList.size(), modelCode);
//                BaseResponse saveResult = assetAutomaticallyEstablishedService.batchSaveToStarRocksAndHBaseWithUpdate(
//                        finalDataList, modelCode, SERVICECLOUD_MODEL_DB);
//
//                if (!saveResult.checkIsSuccess()) {
//                    log.error("[invalidCmdbAsset] Failed to batch save for modelCode [{}]. Response: {}", modelCode, saveResult);
//                }

            } catch (Exception e) {
                log.error("[invalidCmdbAsset] An unexpected error occurred while processing category with aiopsItem: {}", assetCategory.getAiopsItem(), e);
            }
        }

        return BaseResponse.ok();
    }

//    @Override
//    public BaseResponse batchUpdateAssetLevel(AssetLevelBigDataParam param) {
//        try {
//            // 参数校验
//            if (param == null || param.getPrimaryDetailList() == null || param.getPrimaryDetailList().isEmpty()) {
//                return BaseResponse.error("400", "参数不能为空");
//            }
//
//            String modelCode = param.getModelCode();
//            if (modelCode == null || modelCode.trim().isEmpty()) {
//                return BaseResponse.error("400", "modelCode不能为空");
//            }
//
//            // 功能1: 转换数据并保存到StarRocks
//            List<LinkedHashMap<String, Object>> starRocksRows = convertToStarRocksData(param.getPrimaryDetailList());
//            if (!starRocksRows.isEmpty()) {
//                saveToStarRocks(starRocksRows, modelCode);
//            }
//
//            // 功能2: 更新HBase中的数据
//            updateHBaseData(param.getPrimaryDetailList(), modelCode);
//
//            return BaseResponse.ok("批量更新资产等级成功");
//        } catch (Exception e) {
//            log.error("批量更新资产等级失败", e);
//            return BaseResponse.error("500", "批量更新资产等级失败: " + e.getMessage());
//        }
//    }

    @Override
    public BaseResponse batchSaveInstanceToStarRocksAndHBase(AssetSaveBigDataParam param) {
        try {
            // 参数校验
            if (param == null || StringUtils.isEmpty(param.getModelCode()) ||
                    CollectionUtils.isEmpty(param.getAiIdList()) || param.getUpsert() == null ||
                     LongUtil.isEmpty(param.getEid()) || param.getAiopsItem() == null) {
                return BaseResponse.error("500", "参数不能为空");
            }

            String modelCode = param.getModelCode();
            List<Long> aiIdList = param.getAiIdList();
            Boolean upsert = param.getUpsert();

            log.info("Start batch saving instance data, modelCode: {}, aiIdList size: {}, upsert: {}",
                    modelCode, aiIdList.size(), upsert);

            ResponseBase<List<CmdbModelDataFieldRelationMapping>> mappingResponse = assetCategoryService.getCmdbModelDataFieldRelationMappingList(modelCode);
            if (!mappingResponse.checkIsSuccess() || mappingResponse.getData() == null) {
                return BaseResponse.error("500", "Failed to get field relation mapping");
            }

            List<CmdbModelDataFieldRelationMapping> mappingList = mappingResponse.getData();
            if (CollectionUtils.isEmpty(mappingList)) {
                return BaseResponse.error("500", "Field relation mapping configuration not found");
            }

            Map<String, List<CmdbModelDataFieldRelationMapping>> sourceGroupMap = mappingList.stream()
                    .collect(Collectors.groupingBy(CmdbModelDataFieldRelationMapping::getSourceModelCode));

            List<Map<String, Object>> sourceDataList = new ArrayList<>();
            for (Map.Entry<String, List<CmdbModelDataFieldRelationMapping>> entry : sourceGroupMap.entrySet()) {
                String sourceModelCode = entry.getKey();
                List<String> sourceFieldNames = entry.getValue().stream()
                        .map(CmdbModelDataFieldRelationMapping::getSourceModelFieldName)
                        .distinct()
                        .collect(Collectors.toList());

                sourceFieldNames.add("aiId");

                List<Map<String, Object>> sourceData = queryLatestDataFromStarRocks(
                        sourceModelCode, sourceFieldNames, aiIdList);
                sourceDataList.addAll(sourceData);
            }

            if (!upsert) {
                List<Map<String, Object>> additionalData = queryAdditionalAssetData(modelCode, aiIdList, param.getEid(), param.getAiopsItem());
                if (!CollectionUtils.isEmpty(additionalData)) {
                    Map<String, Map<String, Object>> sourceMapIndex = sourceDataList.stream()
                            .filter(map -> map.containsKey("aiId") && map.get("aiId") != null)
                            .collect(Collectors.toMap(
                                    map -> String.valueOf(map.get("aiId")),
                                    Function.identity(),
                                    (existing, replacement) -> existing
                            ));

                    for (Map<String, Object> additionalMap : additionalData) {
                        Object aiIdValue = additionalMap.get("aiId");

                        if (aiIdValue == null) {
                            sourceDataList.add(additionalMap);
                            continue;
                        }

                        String lookupKey = String.valueOf(aiIdValue);
                        Map<String, Object> sourceMapToUpdate = sourceMapIndex.get(lookupKey);

                        if (sourceMapToUpdate != null) {
                            sourceMapToUpdate.putAll(additionalMap);
                        } else {
                            sourceDataList.add(additionalMap);
                        }
                    }
                }
            }

            Map<String, List<CmdbModelDataFieldRelationMapping>> targetGroupMap = mappingList.stream()
                    .collect(Collectors.groupingBy(CmdbModelDataFieldRelationMapping::getTargetModelCode));

            List<Map<String, Object>> targetDataList = new ArrayList<>();
            for (Map.Entry<String, List<CmdbModelDataFieldRelationMapping>> entry : targetGroupMap.entrySet()) {
                String targetModelCode = entry.getKey();
                List<String> targetFieldNames = entry.getValue().stream()
                        .map(CmdbModelDataFieldRelationMapping::getTargetModelFieldName)
                        .distinct()
                        .collect(Collectors.toList());

                targetFieldNames.add("aiId");
                targetFieldNames.add("assetId");

                List<Map<String, Object>> targetData = queryDataFromStarRocks(
                        targetModelCode, targetFieldNames, aiIdList);
                targetDataList.addAll(targetData);
            }

            List<LinkedHashMap<String, Object>> finalDataList;
            if (!upsert) {
                // upsert=false:
                finalDataList = processNewDataOnly(sourceDataList, targetDataList, modelCode,
                        String.valueOf(param.getEid()), param.getAssetId());
            } else {
                // upsert=true:
                finalDataList = processDataMerge(sourceDataList, targetDataList, mappingList);
            }

            if (CollectionUtils.isEmpty(finalDataList)) {
                return BaseResponse.ok("No data to process");
            }

            BaseResponse saveResult = assetAutomaticallyEstablishedService.batchSaveToStarRocksAndHBaseWithUpdate(
                    finalDataList, modelCode, "servicecloud",param.getEid());

            log.info("Batch save instance data completed, processed data count: {}", finalDataList.size());
            return saveResult;

        } catch (Exception e) {
            log.error("Batch save instance data failed", e);
            return BaseResponse.error("500", "Batch save instance data failed: " + e.getMessage());
        }
    }

    /**
     * 功能1: 将primaryDetailList转换为StarRocks需要的数据格式
     */
    @Deprecated
    private List<LinkedHashMap<String, Object>> convertToStarRocksData(List<AssetLevelBigDataParam.AssetLevelPrimaryKeyDetail> primaryDetailList) {
        List<LinkedHashMap<String, Object>> result = new ArrayList<>();

        for (AssetLevelBigDataParam.AssetLevelPrimaryKeyDetail primaryDetail : primaryDetailList) {
            // 创建基础对象，包含主键字段
            LinkedHashMap<String, Object> row = new LinkedHashMap<>();
            row.put(primaryDetail.getFieldName(), primaryDetail.getFieldValue());

            // 添加fieldDetailList中的字段
            if (primaryDetail.getFieldDetailList() != null) {
                for (AssetLevelBigDataParam.AssetLevelBigDataParamFieldDetail fieldDetail : primaryDetail.getFieldDetailList()) {
                    row.put(fieldDetail.getFieldName(), fieldDetail.getFieldValue());
                }
            }

            result.add(row);
        }

        return result;
    }

    /**
     * 功能1: 保存数据到StarRocks
     */
    private void saveToStarRocks(List<LinkedHashMap<String, Object>> rows, String modelCode) throws Exception {
        // 使用bigDataUtil.getStarRocksEntity获取StarRocksEntity
        StarRocksEntity starRocksEntity = bigDataUtil.getStarRocksEntity(rows);

        // 设置其他字段
        starRocksEntity.setDatabase("servicecloud"); // 库名写死为servicecloud
        starRocksEntity.setTable(modelCode); // modelCode作为表名

        // fieldNames已经在getStarRocksEntity中设置，顺序与LinkedHashMap的key顺序一致

        // 调用bigDataUtil的srStreamLoadThrowsException执行保存
        bigDataUtil.srStreamLoadThrowsException(starRocksEntity);

        log.info("成功保存{}条数据到StarRocks表: servicecloud.{}", rows.size(), modelCode);
    }

    /**
     * 功能2: 更新HBase中的数据
     */
//    @Deprecated
//    private void updateHBaseData(List<AssetLevelBigDataParam.AssetLevelPrimaryKeyDetail> primaryDetailList, String modelCode) {
//        try {
//            // 获取sinkFieldsJson配置来构建完整的fieldPath
//            String sinkFieldsJson = etlMapper.getSinkFieldsJson("starrocks", "default", modelCode);
//            List<EtlModelField> etlModelFields = null;
//            if (!StringUtils.isEmpty(sinkFieldsJson)) {
//                etlModelFields = JSON.parseArray(sinkFieldsJson, EtlModelField.class);
//            }
//
//            for (AssetLevelBigDataParam.AssetLevelPrimaryKeyDetail primaryDetail : primaryDetailList) {
//                String id = primaryDetail.getFieldValue(); // id就是primaryDetailList对应的fieldValue
//
//                if (primaryDetail.getFieldDetailList() != null && !primaryDetail.getFieldDetailList().isEmpty()) {
//                    // 准备批量更新的字段映射
//                    Map<String, Object> fieldUpdates = new HashMap<>();
//
//                    for (AssetLevelBigDataParam.AssetLevelBigDataParamFieldDetail fieldDetail : primaryDetail.getFieldDetailList()) {
//                        String fieldName = fieldDetail.getFieldName();
//                        Object newValue = fieldDetail.getFieldValue();
//
//                        // 根据etlModelFields配置构建完整的fieldPath
//                        String fieldPath = buildCompleteFieldPath(fieldName, etlModelFields);
//                        fieldUpdates.put(fieldPath, newValue);
//                    }
//
//                    // 批量更新HBase中的字段
//                    try {
//                        HashMap<String, Object> result = commonUtils.updateMrDetailFields(modelCode, id, fieldUpdates);
//                        if (result != null) {
//                            log.debug("成功更新HBase数据，modelCode: {}, id: {}, 更新字段数: {}",
//                                    modelCode, id, fieldUpdates.size());
//                        } else {
//                            log.warn("更新HBase数据失败，modelCode: {}, id: {}", modelCode, id);
//                        }
//                    } catch (Exception e) {
//                        log.error("更新HBase数据异常，modelCode: {}, id: {}", modelCode, id, e);
//                        // 继续处理下一条记录，不中断整个批量操作
//                    }
//                }
//            }
//        } catch (Exception e) {
//            log.error("获取sinkFieldsJson配置失败，modelCode: {}", modelCode, e);
//        }
//    }

    /**
     * 根据etlModelFields配置构建完整的fieldPath
     *
     * @param fieldName 字段名
     * @param etlModelFields ETL模型字段配置
     * @return 完整的fieldPath
     */
    private String buildCompleteFieldPath(String fieldName, List<EtlModelField> etlModelFields) {
        if (CollectionUtils.isEmpty(etlModelFields)) {
            // 如果没有配置，默认添加field.DataContent前缀
            return "field.DataContent." + fieldName;
        }

        // 查找对应的valuePath配置
        for (EtlModelField etlField : etlModelFields) {
            if (fieldName.equals(etlField.getFieldCode())) {
                String valuePath = etlField.getValuePath();
                if (!StringUtils.isEmpty(valuePath)) {
                    return "field." + valuePath;
                }
            }
        }

        // 如果没有找到配置，默认添加field.DataContent前缀
        return "field.DataContent." + fieldName;
    }

    private List<Map<String, Object>> queryLatestDataFromStarRocks(String tableName, List<String> fieldNames, List<Long> aiIdList) {
        try {
            String fields = String.join(", ", fieldNames);

            String aiIdCondition = aiIdList.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));

            String sql = String.format(
                "SELECT %s FROM (" +
                "  SELECT %s, ROW_NUMBER() OVER (PARTITION BY aiId ORDER BY collectedTime DESC) as rn " +
                "  FROM servicecloud.%s " +
                "  WHERE aiId IN (%s)" +
                ") t WHERE rn = 1",
                fields, fields, tableName, aiIdCondition
            );

            log.debug("Query latest data SQL: {}", sql);
            return bigDataUtil.starrocksQuery(sql);

        } catch (Exception e) {
            log.error("Query StarRocks latest data failed, table: {}, fields: {}", tableName, fieldNames, e);
            return new ArrayList<>();
        }
    }

    private List<Map<String, Object>> queryDataFromStarRocks(String tableName, List<String> fieldNames, List<Long> aiIdList) {
        try {
            String fields = String.join(", ", fieldNames);

            String aiIdCondition = aiIdList.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));

            String sql = String.format(
                "SELECT %s FROM servicecloud.%s WHERE aiId IN (%s)",
                fields, tableName, aiIdCondition
            );

            log.debug("Query data SQL: {}", sql);
            return bigDataUtil.starrocksQuery(sql);

        } catch (Exception e) {
            log.error("Query StarRocks data failed, table: {}, fields: {}", tableName, fieldNames, e);
            return new ArrayList<>();
        }
    }


    private List<LinkedHashMap<String, Object>> processNewDataOnly(
            List<Map<String, Object>> sourceDataList,
            List<Map<String, Object>> targetDataList,
            String modelCode,
            String eid,Long assetId) {
        AssetAttribute assetCodeGenParam = new AssetAttribute();
        AssetAttribute.SrData srData = new AssetAttribute.SrData();
        srData.setTableName(modelCode);
        assetCodeGenParam.setSrData(srData);

        Set<Long> existingAiIds = targetDataList.stream()
                .map(data -> Long.valueOf(data.get("aiId").toString()))
                .collect(Collectors.toSet());

        List<Map<String, Object>> newSourceData = sourceDataList.stream()
                .filter(data -> !existingAiIds.contains(Long.valueOf(data.get("aiId").toString())))
                .collect(Collectors.toList());

        List<LinkedHashMap<String, Object>> result = new ArrayList<>();
        for (Map<String, Object> sourceData : newSourceData) {
            LinkedHashMap<String, Object> newData = new LinkedHashMap<>(sourceData);
            if (LongUtil.isEmpty(assetId)){
                newData.put("assetId", SnowFlake.getInstance().newId());
                // 获取资产编码
                assetRelatedMapService.getNewAssetCode(modelCode, eid, assetCodeGenParam)
                        .ifPresent(assetCode -> newData.put("assetCode", assetCode));
            }else {
                newData.put("assetId", assetId);
            }
            result.add(newData);
        }

        return result;
    }

    private List<LinkedHashMap<String, Object>> processDataMerge(
            List<Map<String, Object>> sourceDataList,
            List<Map<String, Object>> targetDataList,
            List<CmdbModelDataFieldRelationMapping> mappingList) {

        Map<Long, Map<String, Object>> sourceDataMap = sourceDataList.stream()
                .collect(Collectors.toMap(
                    data -> Long.valueOf(data.get("aiId").toString()),
                    data -> data,
                    (existing, replacement) -> existing // If duplicate, keep the first one
                ));

        Map<String, String> fieldMappingMap = mappingList.stream()
                .collect(Collectors.toMap(
                    CmdbModelDataFieldRelationMapping::getSourceModelFieldName,
                    CmdbModelDataFieldRelationMapping::getTargetModelFieldName,
                    (existing, replacement) -> existing // If duplicate, keep the first one
                ));

        List<LinkedHashMap<String, Object>> result = new ArrayList<>();

        for (Map<String, Object> targetData : targetDataList) {
            Long aiId = Long.valueOf(targetData.get("aiId").toString());
            Map<String, Object> sourceData = sourceDataMap.get(aiId);

            if (sourceData != null) {
                LinkedHashMap<String, Object> mergedData = new LinkedHashMap<>(targetData);

                for (Map.Entry<String, Object> sourceEntry : sourceData.entrySet()) {
                    String sourceFieldName = sourceEntry.getKey();
                    Object sourceValue = sourceEntry.getValue();

                    String targetFieldName = fieldMappingMap.get(sourceFieldName);
                    if (targetFieldName != null) {
                        mergedData.put(targetFieldName, sourceValue);
                    } else if (!"aiId".equals(sourceFieldName)) {
                        mergedData.put(sourceFieldName, sourceValue);
                    }
                }

                result.add(mergedData);
            }
        }

        return result;
    }


    public List<Map<String, Object>> queryAdditionalAssetData(String modelCode, List<Long> aiIdList, Long eid, String aiopsItem) {
        try {
            MergeStatisticsDetailRequest request = new MergeStatisticsDetailRequest();
            request.setAiIdList(aiIdList);
            request.setAiopsAuthStatusList(INSTANCE_STATUS_LIST);
            request.setEid(eid);
            request.setAiopsItemList(Stream.of(aiopsItem).collect(Collectors.toList()));

            BaseResponse<List<MergeStatisticsDetailInfo>> response =
                    aioItmsFeignClient.getCommonAiopsInstanceAssetListNoPage(request);

            List<MergeStatisticsDetailInfo> assetInfoList = Optional.ofNullable(response)
                    .map(BaseResponse::getData)
                    .orElse(Collections.emptyList());

            return assetInfoList.stream()
                    .filter(Objects::nonNull)
                    .map(i-> transformAssetInfoToMap(i, eid))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Failed to query additional asset data for modelCode: {}, aiIdList: {}",
                    modelCode, aiIdList, e);
            return new ArrayList<>();
        }
    }

    private Map<String, Object> transformAssetInfoToMap(MergeStatisticsDetailInfo info,Long eid) {
        Map<String, Object> ret = new HashMap<>();
        ret.put("aiId", info.getId());
        ret.put("assetName", info.getDeviceName());
        ret.put("eid", eid);
        ret.put("aiopsItem", info.getAiopsItem());
        ret.put("aiopsItemId", info.getAiopsItemId());
        ret.put("collectedTime", DateUtil.getNowFormatString(DATE_TIME_FORMATTER));
        ret.put("flumeTimestamp", System.currentTimeMillis());
        ret.put("deviceId", getDeviceId(info.getAiopsKitDeviceList()));
        ret.put("operationStatus", determineOperationStatus(info.getAiopsKitDeviceList()));
        ret.put("useStatus", USE_STATUS_IN_USE);
        // kit版本
        ret.put("agentVersion", getDeviceKitVersion(info.getAiopsKitDeviceList()));
        ret.put("managerStatus", MANAGER_STATUS_MAINTENANCE_REQUIRED);
        return ret;
    }

    private String determineOperationStatus(List<AiopsKitDevice> deviceList) {
        if (deviceList == null || deviceList.isEmpty() || deviceList.get(0) == null) {
            // 如果列表为空或第一个元素为null，则无法判断，视为NOT_INSTALLED
            return STATUS_NOT_INSTALLED;
        }

        LocalDateTime lastCheckInTime = deviceList.get(0).getLastCheckInTime();
        if (lastCheckInTime == null) {
            // 如果时间戳为null，也视为NOT_INSTALLED
            return STATUS_NOT_INSTALLED;
        }

        // 计算3天前的时间点
        LocalDateTime threeDaysAgo = LocalDateTime.now().minusDays(3);

        // 如果最后检入时间在3天前的时间点之后，则为Online
        if (lastCheckInTime.isAfter(threeDaysAgo)) {
            return STATUS_ONLINE;
        } else {
            return STATUS_OFFLINE;
        }
    }

    private String getDeviceId(List<AiopsKitDevice> deviceList) {
        if (deviceList == null || deviceList.isEmpty() || deviceList.get(0) == null) {
            return "";
        }

        String deviceId = deviceList.get(0).getDeviceId();
        if (StringUtils.isNotEmpty(deviceId)) {
            return deviceId;
        }

        return "";
    }

    private String getDeviceKitVersion(List<AiopsKitDevice> deviceList) {
        if (deviceList == null || deviceList.isEmpty() || deviceList.get(0) == null) {
            return "";
        }

        String aiopsKitVersion = deviceList.get(0).getAiopskitVersion();
        if (StringUtils.isNotEmpty(aiopsKitVersion)) {
            return aiopsKitVersion;
        }

        return "";
    }

}
