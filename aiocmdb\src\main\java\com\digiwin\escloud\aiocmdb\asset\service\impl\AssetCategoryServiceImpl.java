package com.digiwin.escloud.aiocmdb.asset.service.impl;

import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSON;
import com.digiwin.escloud.aiocmdb.asset.dao.AssetCategoryMapper;
import com.digiwin.escloud.aiocmdb.asset.dao.CmdbModelShowFieldMapper;
import com.digiwin.escloud.aiocmdb.asset.dao.CmdbModelShowFieldUserMapper;
import com.digiwin.escloud.aiocmdb.asset.model.*;
import com.digiwin.escloud.aiocmdb.asset.service.IAssetCategoryService;
import com.digiwin.escloud.aiocmdb.asset.service.AssetService;
import com.digiwin.escloud.aiocmdb.model.dao.ModelMapper;
import com.digiwin.escloud.aiocmdb.model.model.Model;
import com.digiwin.escloud.aiocmdb.model.model.ModelGroup;
import com.digiwin.escloud.aiocmdb.model.model.ModelGroupField;
import com.digiwin.escloud.aiocmdb.assetmaintenance.bigdata.BigDataUtil;
import com.digiwin.escloud.aiocmdb.model.service.IModelService;
import com.digiwin.escloud.aiocmdb.util.DsUtil;
import com.digiwin.escloud.aioitms.model.instance.AiopsBaseInstance;
import com.digiwin.escloud.aioitms.model.instance.MergeStatisticsDetailInfo;
import com.digiwin.escloud.aioitms.model.instance.MergeStatisticsDetailRequest;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.feign.AioItmsFeignClient;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.LongUtil;
import com.digiwin.escloud.common.util.RequestUtil;
import com.digiwin.escloud.common.util.SnowFlake;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.digiwin.escloud.aiocmdb.asset.model.AssetCategory.CreationMode.AUTOMATICALLY_ESTABLISHED;
import static com.digiwin.escloud.aiocmdb.asset.service.impl.AssetServiceImpl.INSTANCE_STATUS_LIST;
import static com.digiwin.escloud.aiocmdb.assetmaintenance.bigdata.BigDataUtil.SERVICECLOUD_MODEL_DB;

/**
 * <p>
 * 资产类别分类表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Service
@Slf4j
public class AssetCategoryServiceImpl  implements IAssetCategoryService ,ParamCheckHelp  {

    private static final String CATEGORY_DEFAULT_SCOPE_ID = "CATEGORY_DEFAULT_SCOPE_ID";
    private static final String ASSET_PROJECT_NAME = "aieom_asset";

    @Autowired
    private AssetCategoryMapper assetCategoryMapper;

    @Autowired
    private CmdbModelShowFieldMapper cmdbModelShowFieldMapper;

    @Autowired
    private CmdbModelShowFieldUserMapper cmdbModelShowFieldUserMapper;

    @Autowired
    private ModelMapper modelMapper;

    @Autowired
    private BigDataUtil bigDataUtil;

    @Autowired
    private DsUtil dsUtil;

    @Value("${api.automatically.established.url:http://221.6.15.182:30010/aiogateway/aiocmdb/asset/assetCategory/ds/process}")
    private String automaticallyEstablishedUrl;

    @Value("${api.automatically.established.crontab:0 0 */3 * * ? *}")
    private String automaticallyEstablishedCrontab;

    @Autowired
    private IModelService modelService;
    @Autowired
    private AioItmsFeignClient aioItmsFeignClient;
    @Autowired
    private AssetService assetService;

    @Override
    public BaseResponse saveAssetCategoryClassification(AssetCategoryClassification classification) {
        // 校验唯一性
        Optional<BaseResponse> optResponse = checkParamIsEmpty(classification.getCategoryType(), "categoryType");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        int count = assetCategoryMapper.countByCategoryName(classification.getCategoryName(),classification.getCategoryType().name() , null);
        if (count > 0) {
            return BaseResponse.error(ResponseCode.ASSET_CATEGORY_CLASSIFICATION_NAME_EXISTS);
        }
        classification.setId(SnowFlake.getInstance().newId());
        int result = assetCategoryMapper.insertAssetCategoryClassification(classification);
        if (result > 0) {
            return BaseResponse.ok(classification);
        }
        return BaseResponse.error(ResponseCode.INSERT_FAILD);
    }

    @Override
    public BaseResponse updateAssetCategoryClassification(AssetCategoryClassification classification) {
        Optional<BaseResponse> optResponse = checkParamIsEmpty(classification.getCategoryType(), "categoryType");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        AssetCategoryClassification categoryClassification = assetCategoryMapper.selectAssetCategoryClassificationById(classification.getId());

        if (BooleanUtil.isFalse(categoryClassification.getCanEdit())) {
            return BaseResponse.error(ResponseCode.ASSET_CATEGORY_CLASSIFICATION_NOT_EDIT_DELETE);
        }
        // 校验categoryName唯一性（排除自己）
        int count = assetCategoryMapper.countByCategoryName(classification.getCategoryName(),classification.getCategoryType().name() , classification.getId());
        if (count > 0) {
            return BaseResponse.error(ResponseCode.ASSET_CATEGORY_CLASSIFICATION_NAME_EXISTS);
        }

        int result = assetCategoryMapper.updateAssetCategoryClassification(classification);
        if (result > 0) {
            return BaseResponse.ok(classification);
        }
        return BaseResponse.error(ResponseCode.UPDATE_FAILD);
    }

    @Override
    public ResponseBase deleteAssetCategoryClassification(Long id) {

        AssetCategoryClassification categoryClassification = assetCategoryMapper.selectAssetCategoryClassificationById(id);

        if (BooleanUtil.isFalse(categoryClassification.getCanDelete())) {
            return ResponseBase.error(ResponseCode.ASSET_CATEGORY_CLASSIFICATION_NOT_EDIT_DELETE);
        }

        // 校验是否有关联的AssetCategory数据
        int count = assetCategoryMapper.countAssetCategoryByClassificationId(id);
        if (count > 0) {
            return ResponseBase.error(ResponseCode.ASSET_CATEGORY_CLASSIFICATION_HAS_CATEGORIES);
        }

        int result = assetCategoryMapper.deleteAssetCategoryClassification(id);
        if (result > 0) {
            return ResponseBase.ok();
        }
        return ResponseBase.error(ResponseCode.DELETE_FAILD);
    }

    @Override
    public BaseResponse getAssetCategoryClassificationList(String categoryType) {
        Optional<BaseResponse> optResponse = checkParamIsEmpty(categoryType, "categoryType");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }
        List<AssetCategoryClassification> list = assetCategoryMapper.selectAssetCategoryClassificationList(categoryType);

        // 为每个分类统计AssetCategory数量
        for (AssetCategoryClassification classification : list) {
            int count = assetCategoryMapper.countAssetCategoryByClassificationId(classification.getId());
            classification.setCount(count);
        }

        return BaseResponse.ok(list);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResponse saveAssetCategory(AssetCategory category) {
        Optional<BaseResponse> optResponse = checkParamIsEmpty(category.getCategoryType(), "categoryType");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        category.setId(SnowFlake.getInstance().newId());
        category.setSid(RequestUtil.getHeaderSid());
        category.setScopeId(CATEGORY_DEFAULT_SCOPE_ID);
        category.setModelCode(category.getCategoryNumber());
        category.setSinkName(category.getCategoryNumber());
        category.setStatus(AssetCategory.Status.ENABLED);
        // 校验sid、scopeId、categoryNumber三字段联合唯一性
        int count = assetCategoryMapper.countBySidScopeIdCategoryNumber(
                category.getSid(), category.getScopeId(), category.getCategoryNumber(), null);
        if (count > 0) {
            return BaseResponse.error(ResponseCode.ASSET_CATEGORY_UNIQUE_CONSTRAINT_VIOLATION);
        }

        int result = assetCategoryMapper.insertAssetCategory(category);

        if (result > 0) {
            // 保存资产类别编号规则
            for (AssetCategoryCodingRuleSettingResult rule : category.getAccrsrList()) {
                rule.setId(SnowFlake.getInstance().newId());
                rule.setObjId(category.getId());
                rule.setObjType(category.getCategoryType().name());
                assetCategoryMapper.insertAssetCategoryCodingRuleSettingResult(rule);
            }

            if (AUTOMATICALLY_ESTABLISHED.equals(category.getCreationMode())) {
                //更新自动映射规则
                BaseResponse baseResponse = saveCmdbModelDataFieldRelationMapping(category.getCmdfrmList());
                if (!baseResponse.checkIsSuccess()){
                    // 回滚
                    throw new RuntimeException("保存CMDB模型字段关系映射失败: " + baseResponse.getErrMsg());
                }

            }
            
            // 根据modelCode查询cmdb_model_group_field并填充到cmdb_model_show_field表
            populateModelShowFields(category);

            // 创建中台任务
            createDsProcess(category);
            return BaseResponse.ok(category);
        }

        return BaseResponse.error(ResponseCode.INSERT_FAILD);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResponse updateAssetCategory(AssetCategory category) {
        // categoryNumber不可编辑，这里不更新categoryNumber字段
        AssetCategory existingCategory = assetCategoryMapper.selectAssetCategoryById(category.getId());
        if (existingCategory == null) {
            return BaseResponse.error(ResponseCode.QUERY_VERIFY);
        }

        int result = assetCategoryMapper.updateAssetCategory(category);
        if (result > 0) {
            assetCategoryMapper.deleteAssetCategoryCodingRuleSettingResult(category.getId());

            // 保存资产类别编号规则
            for (AssetCategoryCodingRuleSettingResult rule : category.getAccrsrList()) {
                rule.setId(SnowFlake.getInstance().newId());
                rule.setObjId(category.getId());
                rule.setObjType(category.getCategoryType().name());
                assetCategoryMapper.insertAssetCategoryCodingRuleSettingResult(rule);
            }
            if (AUTOMATICALLY_ESTABLISHED.equals(category.getCreationMode())) {
                //更新自动映射规则
                BaseResponse baseResponse = saveCmdbModelDataFieldRelationMapping(category.getCmdfrmList());
                if (!baseResponse.checkIsSuccess()){
                    // 回滚
                    throw new RuntimeException("保存CMDB模型字段关系映射失败: " + baseResponse.getErrMsg());
                }
                if (LongUtil.isEmpty(existingCategory.getProcessId())) {
                    // 创建中台任务
                    createDsProcess(category);
                }
            }
            return BaseResponse.ok(category);
        }

        return BaseResponse.error(ResponseCode.UPDATE_FAILD);
    }

    @Override
    public ResponseBase updateAssetCategoryStatus(Long id, String status) {

        AssetCategory.Status newStatus = AssetCategory.Status.valueOf(status);
        // 1. 查询对象
        AssetCategory category = assetCategoryMapper.selectAssetCategoryById(id);
        if (category == null) {
            return ResponseBase.error(ResponseCode.QUERY_VERIFY);
        }

        // 标记是否为“停用”操作，提高可读性，避免重复判断
        boolean isDisabling = newStatus == AssetCategory.Status.DISABLED;

        // 2. 停用前的业务校验
        if (isDisabling) {
            int count = countAssetBySinkName(category.getSinkName());
            if (count > 0) {
                return ResponseBase.error(ResponseCode.ASSET_CATEGORY_HAS_ASSET);
            }
        }

        // 3. 更新数据库状态
        category.setStatus(newStatus);
        int result = assetCategoryMapper.updateAssetCategory(category);

        // 如果数据库更新失败，直接返回错误，不执行后续操作
        if (result <= 0) {
            return ResponseBase.error(ResponseCode.UPDATE_FAILD);
        }

        // 4. 数据库更新成功后，再执行与下游系统的交互（关键逻辑修正）
        updateDsSystem(category, isDisabling);

        // 5. 所有操作成功
        return ResponseBase.ok(category);
    }

    /**
     * 私有辅助方法，封装与下游系统交互的逻辑，使主流程更清晰
     * @param category 资产分类对象
     * @param isDisabling 是否是停用操作
     */
    private void updateDsSystem(AssetCategory category, boolean isDisabling) {
        if (isDisabling) {
            offLineDsProcess(category);
        } else {
            onLineDsProcess(category);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseBase deleteAssetCategory(Long id) {
        AssetCategory category = assetCategoryMapper.selectAssetCategoryById(id);
        if (category == null) {
            return ResponseBase.error(ResponseCode.QUERY_VERIFY);
        }

        if (category.getStatus().equals(AssetCategory.Status.ENABLED)) {
            return ResponseBase.error(ResponseCode.DELETE_FAILD);
        }

        // 删除前需要进行额外的校验，预留位置
        int count = countAssetBySinkName(category.getSinkName());

        if (count > 0) {
            return ResponseBase.error(ResponseCode.ASSET_CATEGORY_HAS_ASSET);
        }
        int result = assetCategoryMapper.deleteAssetCategory(id);

        if (result > 0) {
            assetCategoryMapper.deleteAssetCategoryCodingRuleSettingResult(id);

            // 删除中台定时任务
            deleteDsProcess(category);
            // 删除cmdb_model_show_field表
            cmdbModelShowFieldMapper.deleteByModelCode(category.getModelCode(), category.getSid());
            cmdbModelShowFieldUserMapper.deleteByModelCode(category.getModelCode(), category.getSid());
            // 清空模型数据
            bigDataUtil.srSave(" truncate table " + SERVICECLOUD_MODEL_DB + category.getModelCode());
            return ResponseBase.ok();
        }

        return ResponseBase.error(ResponseCode.DELETE_FAILD);
    }

    @Override
    public BaseResponse getAssetCategoryList(AssetCategoryQueryParam queryParam) {
        Optional<BaseResponse> optResponse = checkParamIsEmpty(queryParam.getCategoryType(), "categoryType");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        if (queryParam.getNeedPaging()) {
            // 分页查询
            int pageNum = queryParam.getPageNum() == null || queryParam.getPageNum() <= 0 ? 1 : queryParam.getPageNum();
            int pageSize = queryParam.getPageSize() == null || queryParam.getPageSize() <= 0 ? 10 : queryParam.getPageSize();

            PageHelper.startPage(pageNum, pageSize);
            List<AssetCategory> list = assetCategoryMapper.selectAssetCategoryListWithPaging(queryParam);
            PageInfo<AssetCategory> pageInfo = new PageInfo<>(list);

            if (list.isEmpty()) {
                return BaseResponse.ok(pageInfo);
            }

            // 填充相关数据
            populateAssetCategoryData(list);

            return BaseResponse.ok(pageInfo);
        } else {
            // 不分页查询，只支持根据classificationId搜索
            List<AssetCategory> list = assetCategoryMapper.selectAssetCategoryListWithoutPaging(queryParam.getClassificationId(),
                    queryParam.getCategoryType(), queryParam.getCategoryNumber());

            // 填充相关数据
            populateAssetCategoryData(list);

            return BaseResponse.ok(list);
        }
    }

    /**
     * 填充AssetCategory相关数据：Count、ModelData、CmdfrmList、AccrsrList
     *
     * @param list AssetCategory列表
     */
    private void populateAssetCategoryData(List<AssetCategory> list) {
        if (list.isEmpty()) {
            return;
        }

        // 提取modelCode列表
        List<String> modelCodeList = list.stream()
                .map(AssetCategory::getModelCode)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        // 获取StarRocks中的数量统计
        Map<String, Long> starrocksCountMap = getStarRocksCountMap(modelCodeList);

        // 获取Model数据映射
        Map<String, Model> modelMap = getModelMap(modelCodeList);

        // 为每个category填充数据
        for (AssetCategory category : list) {
            // 设置编码规则数据
            List<AssetCategoryCodingRuleSettingResult> ruleResults =
                    assetCategoryMapper.selectAssetCategoryCodingRuleSettingResultByObjId(category.getId());
            category.setAccrsrList(ruleResults);

            // 设置映射规则数据
            List<CmdbModelDataFieldRelationMapping> cmdfrmList =
                    assetCategoryMapper.selectCmdbModelDataFieldRelationMappingByTargetModelCode(category.getModelCode());
            category.setCmdfrmList(cmdfrmList);

            // 设置模型数据
            category.setModelData(modelMap.get(category.getModelCode()));

            // 设置数量
            if (category.getModelCode() != null) {
                category.setCount(starrocksCountMap.getOrDefault(category.getModelCode(), 0L));
            }
        }
    }

    /**
     * 获取StarRocks中各modelCode对应的数量统计
     *
     * @param modelCodeList modelCode列表
     * @return modelCode到数量的映射
     */
    private Map<String, Long> getStarRocksCountMap(List<String> modelCodeList) {
        Map<String, Long> starrocksCountMap = new HashMap<>();

        if (!modelCodeList.isEmpty()) {
            StringJoiner sqlBuilder = new StringJoiner(" UNION ALL ");
            for (String modelCode : modelCodeList) {
                sqlBuilder.add(String.format("SELECT '%s' as model_code, COUNT(*) as count FROM %s%s",
                        modelCode, SERVICECLOUD_MODEL_DB, modelCode));
            }
            String finalSql = sqlBuilder.toString();

            List<Map<String, Object>> countResults = bigDataUtil.starrocksQuery(finalSql);

            starrocksCountMap = countResults.stream()
                    .collect(Collectors.toMap(
                            map -> (String) map.get("model_code"),
                            map -> {
                                Object countValue = map.get("count");
                                return (countValue instanceof Number) ? ((Number) countValue).longValue() : 0L;
                            }
                    ));
        }

        return starrocksCountMap;
    }

    /**
     * 获取Model数据映射
     *
     * @param modelCodeList modelCode列表
     * @return modelCode到Model对象的映射
     */
    private Map<String, Model> getModelMap(List<String> modelCodeList) {
        if (modelCodeList.isEmpty()) {
            return new HashMap<>();
        }

        List<Model> modelList = assetCategoryMapper.selectByModelCodeList(modelCodeList);
        return modelList.stream()
                .collect(Collectors.toMap(Model::getModelCode, model -> model));
    }

    @Override
    @Transactional
    public BaseResponse saveCmdbModelDataFieldRelationMapping(List<CmdbModelDataFieldRelationMapping> mappingList) {
        if (mappingList == null || mappingList.isEmpty()) {
            return BaseResponse.error(ResponseCode.PARAM_VERIFY);
        }

        String targetModelCode = mappingList.get(0).getTargetModelCode();
        if (!StringUtils.hasText(targetModelCode)) {
            return BaseResponse.error(ResponseCode.PARAM_VERIFY);
        }

        // 根据targetModelCode删除表里所有的数据
        assetCategoryMapper.deleteCmdbModelDataFieldRelationMappingByTargetModelCode(targetModelCode);

        // 新增数据，同时校验targetModelCode和targetModelFieldName的联合唯一性
        for (CmdbModelDataFieldRelationMapping mapping : mappingList) {
            int count = assetCategoryMapper.countByTargetModelCodeAndFieldName(
                    mapping.getTargetModelCode(), mapping.getTargetModelFieldName(),mapping.getTargetModelFieldJsonPath() , null);
            if (count > 0) {
                return BaseResponse.error(ResponseCode.SOMTHING_ALREADY_EXISTS,
                        "targetModelCode and targetModelFieldName combination");
            }
            mapping.setId(SnowFlake.getInstance().newId());
            assetCategoryMapper.insertCmdbModelDataFieldRelationMapping(mapping);
        }

        return BaseResponse.ok(mappingList);
    }

    @Override
    public ResponseBase<List<CmdbModelDataFieldRelationMapping>> getCmdbModelDataFieldRelationMappingList(String targetModelCode) {
        if (!StringUtils.hasText(targetModelCode)) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY);
        }

        List<CmdbModelDataFieldRelationMapping> list =
                assetCategoryMapper.selectCmdbModelDataFieldRelationMappingByTargetModelCode(targetModelCode);
        return ResponseBase.okT(list);
    }

    @Override
    public ResponseBase getAllAssetCategoryCodingRule() {
        List<AssetCategoryCodingRule> list =
                assetCategoryMapper.selectAllAssetCategoryCodingRule();
        return ResponseBase.ok(list);
    }

    @Override
    @Transactional
    public ResponseBase batchUpsertAiopsCollectSink(List<AiopsCollectSink> sinkList) {
        if (sinkList == null || sinkList.isEmpty()) {
            return ResponseBase.ok();
        }
        for (AiopsCollectSink aiopsCollectSink : sinkList) {
            aiopsCollectSink.setId(SnowFlake.getInstance().newId());
        }
        assetCategoryMapper.batchUpsertAiopsCollectSink(sinkList);
        return ResponseBase.ok();
    }

    @Override
    public ResponseBase getAllModel(String modelGroupCode, String modelCode) {
        List<ModelGroup> allModelGroups = modelService.getAllModel(null, modelGroupCode, null, true, null);

        if (allModelGroups == null || allModelGroups.isEmpty()) {
            return ResponseBase.ok(Collections.emptyList());
        }

        long headerSid = RequestUtil.getHeaderSid();
        List<String> modelCodeList = assetCategoryMapper.selectCategoryNumberBySidScopeIdCategoryNumber(headerSid, CATEGORY_DEFAULT_SCOPE_ID);

        Set<String> allowedCodesSet = new HashSet<>(modelCodeList);
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(modelCode)) {
            allowedCodesSet.remove(modelCode);
        }
        allModelGroups.forEach(group -> {
            if (group != null && group.getModellist() != null) {
                List<Model> filteredModels = group.getModellist().stream()
                        .filter(model -> !allowedCodesSet.contains(model.getModelCode()))
                        .collect(Collectors.toList());
                group.setModellist(filteredModels);
            }
        });

        return ResponseBase.ok(allModelGroups);
    }

    /**
     * 根据sinkName（表名）从StarRocks中计算资产数量。
     *
     * @param sinkName 要查询的表名。
     * @return 资产的数量。如果查询无结果或发生错误，则返回0。
     */
    private int countAssetBySinkName(String sinkName) {
        // --- 安全性检查 ---
        if (sinkName == null || sinkName.trim().isEmpty()) {
            return 0;
        }

        // 拼接SQL语句
        String sql = "select count(1) as cnt from " + SERVICECLOUD_MODEL_DB + "." + sinkName;

        try {
            List<Map<String, Object>> maps = bigDataUtil.starrocksQuery(sql);

            if (maps == null || maps.isEmpty()) {
                log.warn("StarRocks query returned no results for SQL: {}", sql);
                return 0;
            }

            // 2. 获取第一行数据（也应该是唯一一行）
            Map<String, Object> row = maps.get(0);
            if (row == null || !row.containsKey("cnt")) {
                log.error("Expected column 'cnt' not found in StarRocks query result for SQL: {}", sql);
                return 0;
            }

            // 3. 安全地将结果转换为int
            Object countValue = row.get("cnt");
            if (countValue instanceof Number) {
                return ((Number) countValue).intValue();
            } else if (countValue != null) {
                return Integer.parseInt(countValue.toString());
            } else {
                return 0;
            }

        } catch (NumberFormatException e) {
            log.error("Failed to parse count value from StarRocks. SQL: {}. Error: {}", sql, e.getMessage());
            return 0;
        } catch (Exception e) {
            log.error("An error occurred while counting assets from StarRocks. SQL: {}. Error: {}", sql, e.getMessage(), e);
            return 0;
        }
    }

    private void createDsProcess(AssetCategory assetCategory) {
        if (AUTOMATICALLY_ESTABLISHED.equals(assetCategory.getCreationMode())) {
            HashMap<String, Object> dsParamMap = buildProcessParams(assetCategory);
            dsUtil.createProcessAsync(dsParamMap, processId -> {
                assetCategoryMapper.updateAssetCategoryProcessId(assetCategory.getId(), processId);
                dsUtil.onLineProcessAsync(processId, result -> {
                    log.info("[createDsProcess] create process result: {}", result);
                });
            }, ASSET_PROJECT_NAME);
        }

    }

    private void updateDsProcess(AssetCategory assetCategory) {
        if (extracted(assetCategory)) {
            return;
        }
        if (AUTOMATICALLY_ESTABLISHED.equals(assetCategory.getCreationMode())) {
            HashMap<String, Object> dsParamMap = buildProcessParams(assetCategory);
            dsUtil.updateProcessAsync(dsParamMap, result -> {
                log.info("[updateDsProcess] update process result: {}", result);
            }, ASSET_PROJECT_NAME);
        } else {
            deleteDsProcess(assetCategory);
        }

    }

    private static boolean extracted(AssetCategory assetCategory) {
        return assetCategory.getProcessId() == null || assetCategory.getProcessId() == 0;
    }

    private void offLineDsProcess(AssetCategory assetCategory) {
        if (extracted(assetCategory)) {
            return;
        }
        dsUtil.offLineProcessAsync(assetCategory.getProcessId(), aBoolean -> {
            log.info("[offLineDsProcess] offLineProcessAsync result: {} , processId:{}", aBoolean, assetCategory.getProcessId());
        });
    }

    private void onLineDsProcess(AssetCategory assetCategory) {
        if (extracted(assetCategory)) {
            return;
        }
        dsUtil.onLineProcessAsync(assetCategory.getProcessId(), aBoolean -> {
            log.info("[onLineDsProcess] onLineProcessAsync result: {} , processId:{}", aBoolean, assetCategory.getProcessId());
        });
    }

    private void deleteDsProcess(AssetCategory assetCategory) {
        if (extracted(assetCategory)) {
            return;
        }
        dsUtil.deleteProcessAsync(assetCategory.getProcessId(), aBoolean -> {
            log.info("[deleteDsProcess] deleteProcessAsync result: {} , processId:{}", aBoolean, assetCategory.getProcessId());
        },  ASSET_PROJECT_NAME);
    }

    private HashMap<String, Object> buildProcessParams(AssetCategory assetCategory) {
        HashMap<String, Object> params = new HashMap<>(8);
        params.put("id", assetCategory.getId());
        params.put("name", assetCategory.getCategoryNumber());
        params.put("crontab", automaticallyEstablishedCrontab);
        params.put("type", ASSET_PROJECT_NAME);
        params.put("appCode",ASSET_PROJECT_NAME);
        params.put("automaticallyEstablishedUrl", automaticallyEstablishedUrl);
        Map<String, Long> map = new HashMap<>();
        map.put("acId", assetCategory.getId());
        params.put("requestParamsBody", JSON.toJSONString(map));
        params.put("processId", assetCategory.getProcessId());
        return params;
    }

    /**
     * 根据category.modelCode查询cmdb_model_group_field并填充到cmdb_model_show_field表
     *
     * @param category 资产类别
     */
    private void populateModelShowFields(AssetCategory category) {
        try {
            String modelCode = category.getModelCode();
            if (StringUtils.isEmpty(modelCode)) {
                log.warn("[populateModelShowFields] modelCode is empty for category: {}", category.getId());
                return;
            }

            // 0. 先删除已存在的数据
            cmdbModelShowFieldMapper.deleteByModelCode(modelCode, category.getSid());
            log.info("[populateModelShowFields] Deleted existing model show fields for modelCode: {}", modelCode);

            // 1. 根据modelCode查询模型信息获取modelGroupCode
            Model model = modelMapper.getByCode(modelCode);
            if (model == null) {
                log.warn("[populateModelShowFields] Model not found for modelCode: {}", modelCode);
                return;
            }

            String modelGroupCode = model.getModelGroupCode();
            if (StringUtils.isEmpty(modelGroupCode)) {
                log.warn("[populateModelShowFields] modelGroupCode is empty for modelCode: {}", modelCode);
                return;
            }

            // 2. 根据modelGroupCode查询cmdb_model_group_field获取字段信息，设置默认customHide为false
            List<ModelGroupField> modelGroupFields = modelMapper.getModelGroupField(modelGroupCode);
            List<CmdbModelShowField> showFields = new ArrayList<>();

            if (modelGroupFields != null && !modelGroupFields.isEmpty()) {
                for (ModelGroupField groupField : modelGroupFields) {
                    CmdbModelShowField showField = new CmdbModelShowField();
                    showField.setId(SnowFlake.getInstance().newId());
                    showField.setSid(category.getSid());
                    showField.setModelCode(modelCode);
                    showField.setFieldCode(groupField.getFieldCode());
                    showField.setSort(groupField.getSort());
                    showField.setModelFieldGroupCode(modelGroupCode);
                    showField.setCustomHide(false); // 默认customHide为false
                    showFields.add(showField);
                }
            }

            // 3. 根据modelCode查询cmdb_model_field_mapping表中的targetCode
            List<String> targetCodes = modelMapper.selectTargetCodesByModelCode(modelCode, category.getSid());
            if (targetCodes != null && !targetCodes.isEmpty()) {
                // 过滤掉已存在的fieldCode
                Set<String> existingFieldCodes = showFields.stream()
                        .map(CmdbModelShowField::getFieldCode)
                        .collect(Collectors.toSet());

                int maxSort = showFields.stream()
                        .mapToInt(CmdbModelShowField::getSort)
                        .max()
                        .orElse(0);

                for (String targetCode : targetCodes) {
                    if (!existingFieldCodes.contains(targetCode)) {
                        CmdbModelShowField showField = new CmdbModelShowField();
                        showField.setId(SnowFlake.getInstance().newId());
                        showField.setSid(category.getSid());
                        showField.setModelCode(modelCode);
                        showField.setFieldCode(targetCode);
                        showField.setSort(++maxSort);
                        showField.setModelFieldGroupCode(modelGroupCode);
                        showField.setCustomHide(true); // 默认customHide为true
                        showFields.add(showField);
                    }
                }
            }

            // 4. 批量插入到cmdb_model_show_field表
            if (!showFields.isEmpty()) {
                int insertCount = cmdbModelShowFieldMapper.batchInsert(showFields);
                log.info("[populateModelShowFields] Successfully inserted {} model show fields for modelCode: {}",
                        insertCount, modelCode);
            } else {
                log.info("[populateModelShowFields] No model show fields to insert for modelCode: {}", modelCode);
            }

        } catch (Exception e) {
            log.error("[populateModelShowFields] Error populating model show fields for category: {}",
                    category.getId(), e);
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBase populateModelShowFields(String modelCode) {
        if (StringUtils.isEmpty(modelCode)) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY);
        }

        try {
            // 创建一个临时的AssetCategory对象来调用私有方法
            AssetCategory tempCategory = new AssetCategory();
            tempCategory.setModelCode(modelCode);
            tempCategory.setSid(RequestUtil.getHeaderSid());

            populateModelShowFields(tempCategory);

            return ResponseBase.ok();
        } catch (Exception e) {
            log.error("[populateModelShowFields] Error populating model show fields for modelCode: {}", modelCode, e);
            return ResponseBase.error(ResponseCode.INTERNAL_ERROR);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBase deleteCmdbModelShowFieldUser(String userId,String modelCode) {
        if (StringUtils.isEmpty(userId)) {
            return ResponseBase.error(ResponseCode.PARAM_VERIFY);
        }

        try {
            int deleteCount = cmdbModelShowFieldUserMapper.deleteByUserId(userId,modelCode);
            log.info("[deleteCmdbModelShowFieldUserByUserId] Successfully deleted {} user field configurations for userId: {}",
                    deleteCount, userId);
            return ResponseBase.ok();
        } catch (Exception e) {
            log.error("[deleteCmdbModelShowFieldUserByUserId] Error deleting user field configurations for userId: {}", userId, e);
            return ResponseBase.error(ResponseCode.INTERNAL_ERROR);
        }
    }

    @Override
    public ResponseBase dsProcess(AiopsProcessParam param) {
        try {
            // 1. 根据acId查询asset_category表获取aiopsItem和modelCode
            AssetCategory assetCategory = assetCategoryMapper.selectAssetCategoryById(param.getAcId());
            if (assetCategory == null) {
                return ResponseBase.error(ResponseCode.QUERY_VERIFY);
            }

            String aiopsItem = assetCategory.getAiopsItem();
            String modelCode = assetCategory.getModelCode();

            // 2. 去StarRocks中查询该modelCode，过滤条件是aiId不等于空，useStatus in ('InUse')
            String sql = String.format(
                "SELECT eid, aiId FROM %s%s WHERE aiId IS NOT NULL AND useStatus = 'InUse'",
                    SERVICECLOUD_MODEL_DB, modelCode);
            List<Map<String, Object>> starRocksResult = bigDataUtil.starrocksQuery(sql);

            // 按租户分组，聚合实例id为aiIdList
            Map<Long, List<Long>> eidToAiIdListMap = starRocksResult.stream()
                .collect(Collectors.groupingBy(
                    row -> Long.valueOf(row.get("eid").toString()),
                    Collectors.mapping(
                        row -> Long.valueOf(row.get("aiId").toString()),
                        Collectors.toList()
                    )
                ));

            // 3. 调用getCommonAiopsInstanceAssetListNoPage获取资产实例
            MergeStatisticsDetailRequest request = new MergeStatisticsDetailRequest();
            request.setAiopsItemList(Stream.of(aiopsItem).collect(Collectors.toList()));
            request.setAiopsAuthStatusList(INSTANCE_STATUS_LIST);

            // 收集所有aiId用于查询
            List<Long> allAiIdList = eidToAiIdListMap.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());
            request.setNotInAiIdList(allAiIdList);

            BaseResponse<List<MergeStatisticsDetailInfo>> response =
                    aioItmsFeignClient.getCommonAiopsInstanceAssetListNoPage(request);

            if (!response.checkIsSuccess() || response.getData() == null) {
                log.error("[dsProcess] getCommonAiopsInstanceAssetListNoPage failed or returned null data: {}", response);
                return ResponseBase.error(ResponseCode.INTERNAL_ERROR);
            }

            List<MergeStatisticsDetailInfo> detailInfoList = response.getData();

            Map<Long, List<Long>> eidToAiIdsMap = detailInfoList.stream()
                    .collect(Collectors.groupingBy(
                            MergeStatisticsDetailInfo::getEid,
                            Collectors.mapping(MergeStatisticsDetailInfo::getId, Collectors.toList())
                    ));
            // 4. 保存自动上报得资产 upsert=false
            for (Map.Entry<Long, List<Long>> e : eidToAiIdsMap.entrySet()) {
                Long key = e.getKey();
                List<Long> idListForEid = e.getValue();
                if (idListForEid == null || idListForEid.isEmpty()) {
                    continue;
                }

                log.info("[dsProcess] Processing batch save for EID: {}, with {} instances.", key, idListForEid.size());

                AssetSaveBigDataParam saveParam = new AssetSaveBigDataParam();
                saveParam.setModelCode(modelCode);
                saveParam.setUpsert(false);
                saveParam.setAiIdList(idListForEid);
                saveParam.setAiopsItem(aiopsItem);
                saveParam.setEid(key);

                BaseResponse saveResult = assetService.batchSaveInstanceToStarRocksAndHBase(saveParam);

                if (!saveResult.checkIsSuccess()) {
                    log.error("[dsProcess] batchSaveInstanceToStarRocksAndHBase failed for EID: {}. Result: {}", key, saveResult);
                }
            }

            // 5. 按租户循环调用batchSaveInstanceToStarRocksAndHBase，upsert=true
            for (Map.Entry<Long, List<Long>> entry : eidToAiIdListMap.entrySet()) {
                Long eid = entry.getKey();
                List<Long> aiIdList = entry.getValue();


                if (!aiIdList.isEmpty()) {
                    AssetSaveBigDataParam saveParam2 = new AssetSaveBigDataParam();
                    saveParam2.setModelCode(modelCode);
                    saveParam2.setUpsert(true);
                    saveParam2.setAiIdList(aiIdList);
                    saveParam2.setEid(eid);
                    saveParam2.setAiopsItem(aiopsItem);

                    BaseResponse saveResult2 = assetService.batchSaveInstanceToStarRocksAndHBase(saveParam2);
                    if (!saveResult2.checkIsSuccess()) {
                        log.error("[dsProcess]租户{}保存失败: {}", eid, saveResult2.getErrMsg());
                    }
                }
            }

            return ResponseBase.ok();

        } catch (Exception e) {
            log.error("[dsProcess] dsProcess处理失败", e);
            return ResponseBase.error(ResponseCode.INTERNAL_ERROR);
        }
    }
}
