package com.digiwin.escloud.aiocmdb.report.controller;

import com.digiwin.escloud.aiocmdb.report.model.*;
import com.digiwin.escloud.aiocmdb.report.model.dto.MISStatus;
import com.digiwin.escloud.aiocmdb.report.model.dto.ReportMaintenanceUpdateDTO;
import com.digiwin.escloud.aiocmdb.report.service.IReportService;
import com.digiwin.escloud.common.controller.ControllerBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.util.LongUtil;
import com.digiwin.escloud.common.util.RequestUtil;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;

@Api(value = "资产维护报表", tags = {"REPORT"})
@Slf4j
@RestController
@RequestMapping("/api/report")
public class ReportController extends ControllerBase {

    @Autowired
    private IReportService reportService;

    @ApiOperation(value = "获取报表接收人")
    @GetMapping(value = "/receivers")
    public BaseResponse getReceivers(@ApiParam(required = true, value = "客代") @RequestParam(value = "serviceCode", required = false, defaultValue = "") String serviceCode,
                                     @ApiParam(required = false, value = "页码") @RequestParam(value = "page", required = false, defaultValue = "0") int page,
                                     @ApiParam(required = false, value = "条数") @RequestParam(value = "size", required = false, defaultValue = "0") int size,
                                     @ApiParam(required = false, value = "模组code") @RequestParam(value = "moduleCode", required = false) String moduleCode) {
        BaseResponse res = new BaseResponse();
        try {
            res.setCode(ResponseCode.SUCCESS.toString());
            //如果是服务商平台 且模块未传 只能查询漏扫模块报告接收人
            if (LongUtil.isNotEmpty(RequestUtil.getHeaderServiceProviderSid())&& org.apache.commons.lang3.StringUtils.isBlank(moduleCode)) {
                moduleCode = "vulnerability";
            }
            List<ReportReceiver> receivers = reportService.getReceivers(serviceCode, moduleCode, page, size);
            int receiversCount = reportService.getReceiversCount(serviceCode, moduleCode);
            ReportReceiversGetRes reportReceiversGetRes = new ReportReceiversGetRes(receivers, receiversCount);
            res.setData(reportReceiversGetRes);
        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }
        return res;
    }

    @ApiOperation(value = "获取接收人模组")
    @GetMapping(value = "/modules")
    public BaseResponse getModules() {
        BaseResponse res = new BaseResponse();
        try {
            res.setCode(ResponseCode.SUCCESS.toString());
            res.setData(reportService.getModules());
        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }
        return res;
    }

    @ApiOperation(value = "保存报表接收人")
    @PostMapping(value = "/receiver/save")
    public BaseResponse saveReceiver(@ApiParam(required = true, value = "维护状况记录") @RequestBody ReportReceiver reportReceiver) {
        BaseResponse res = new BaseResponse();
        try {
            res.setCode(ResponseCode.SUCCESS.toString());
            res.setData(reportService.saveReceiver(reportReceiver));
        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }
        return res;
    }

    @ApiOperation(value = "删除报表接收人")
    @DeleteMapping(value = "/receiver/{id}/del")
    public BaseResponse deleteReceiver(@ApiParam(required = true, value = "id") @PathVariable(value = "id", required = true) long id) {
        BaseResponse res = new BaseResponse();
        try {
            res.setCode(ResponseCode.SUCCESS.toString());
            res.setData(reportService.deleteReceiver(id));
        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }
        return res;
    }

    @ApiOperation(value = "获取资产维护报表")
    @GetMapping(value = "/{serviceCode}/list")
    public BaseResponse getReports(@ApiParam(required = true, value = "客代") @PathVariable(value = "serviceCode", required = true) String serviceCode,
                                   @ApiParam(required = false, value = "状态") @RequestParam(required = false) Integer reportStatus,
                                   @ApiParam(required = false, value = "MIS端發送状态") @RequestParam(required = false) Integer misStatus,
                                   @ApiParam(required = false, value = "开始时间") @RequestParam(value = "beginTime", required = false, defaultValue = "") String beginTime,
                                   @ApiParam(required = false, value = "结束时间") @RequestParam(value = "endTime", required = false, defaultValue = "") String endTime,
                                   @ApiParam(required = false, value = "页码") @RequestParam(value = "page", required = false, defaultValue = "0") int page,
                                   @ApiParam(required = false, value = "条数") @RequestParam(value = "size", required = false, defaultValue = "0") int size) {
        BaseResponse res = new BaseResponse();
        try {
            res.setCode(ResponseCode.SUCCESS.toString());
            int reportsCount = reportService.getReportsCount(serviceCode,reportStatus, misStatus, beginTime, endTime);
            List<ReportMaintenance> reports = reportService.getReports(serviceCode,reportStatus, misStatus, beginTime, endTime, page, size);
            ReportMaintenancesGetRes reportMaintenancesGetRes = new ReportMaintenancesGetRes(reports, reportsCount);
            res.setData(reportMaintenancesGetRes);
        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }
        return res;
    }

    @ApiOperation(value = "(取消）作废资产维护报表")
    @PostMapping(value = "/maintenance/update")
    public BaseResponse update(@RequestBody ReportMaintenanceUpdateDTO dto) {
        BaseResponse res = new BaseResponse();
        try {
            res.setCode(ResponseCode.SUCCESS.toString());
            int reportsCount = reportService.updateReportMaintenance(dto);
            res.setData(reportsCount);
        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }
        return res;
    }

    @ApiOperation(value = "获取资产维护报表log")
    @GetMapping(value = "/maintenance/log/{reportMaintenanceId}")
    public BaseResponse getReportLogs(@PathVariable Long reportMaintenanceId){
        BaseResponse res = new BaseResponse();
        try {
            res.setCode(ResponseCode.SUCCESS.toString());
            List<ReportMaintenanceLog> reportLogs = reportService.getReportLogs(reportMaintenanceId);
            res.setData(reportLogs);
        } catch (Exception ex) {
            log.error(ex.getMessage());
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
        }
        return res;
    }

    @ApiOperation(value = "获取资产维护报表邮件接收人")
    @GetMapping(value = "/{rmId}/receivers")
    public BaseResponse getReportReceivers(
            @ApiParam(required = true, value = "资产维护报表Id")
            @PathVariable(value = "rmId") Long rmId) {
        return getBaseResponse(() -> reportService.getReceiversByRmId(rmId),
                false, false, null);
    }

    @ApiOperation(value = "发送报告")
    @PostMapping(value = "/send")
    public BaseResponse sendReport(@ApiParam(required = true, value = "维护状况记录") @RequestBody ReportMaintenance rm) {
        try {
            return reportService.sendReport(rm);
        } catch (Exception ex) {
            log.error(ex.getMessage());
            BaseResponse res = new BaseResponse();
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
            return res;
        }
    }

    @ApiOperation(value = "维护总报表下载/软硬件资产报表下载")
    @PostMapping(value = "/download")
    public BaseResponse downloadReport(@ApiParam(required = true, value = "维护状况记录") @RequestBody ReportTemp rt) {
        try {
            return reportService.downloadReport(rt);
        } catch (Exception ex) {
            log.error(ex.getMessage());
            BaseResponse res = new BaseResponse();
            res.setCode(ResponseCode.INTERNAL_ERROR.toString());
            res.setErrMsg(ex.getMessage());
            return res;
        }
    }

    @ApiOperation(value = "获取资产维护报表详细数据")
    @GetMapping(value = "/data")
    public Object GetMaintenancestateReport(@ApiParam(required = true, value = "报表临时表ID") @RequestParam(value = "reportTempId", required = false, defaultValue = "") String reportTempId,
                                            @ApiParam(required = true, value = "模型编号") @RequestParam(value = "modelCode", required = false, defaultValue = "") String modelCode,
                                            @ApiParam(required = true, value = "设备ID") @RequestParam(value = "deviceId", required = false, defaultValue = "") String deviceId,
                                            @ApiParam(required = true, value = "年月") @RequestParam(value = "month", required = false, defaultValue = "") String yearMonth,
                                            @ApiParam(required = true, value = "数据类别") @RequestParam(value = "type", required = false, defaultValue = "") String type,
                                            @ApiParam(required = true, value = "数据子集") @RequestParam(value = "item", required = false, defaultValue = "") String item,
                                            @ApiParam(required = true, value = "客服代号") @RequestParam(value = "serviceCode", required = false, defaultValue = "02100251") String serviceCode) {
        Gson gson = new Gson();
        JsonArray jsonArray = new JsonArray();
        if (modelCode.equals("BackupSchedule") || modelCode.equals("Software_Hardware")) {
            jsonArray = reportService.GetMaintenancestateReportDataByAllDevice(serviceCode, modelCode, yearMonth, item);
        } else if (StringUtils.isEmpty(reportTempId)) {
            if (StringUtils.isEmpty(item)) {
                jsonArray = reportService.GetMaintenancestateReportDataByDeviceId(serviceCode, deviceId, modelCode, yearMonth, type);
            } else if (item.startsWith("relation_")) {
                jsonArray = reportService.getMaintenancestateReportDataByDeviceIdByRelationArrayItem(serviceCode, deviceId, modelCode, yearMonth, type, item);
            } else
                jsonArray = reportService.GetGetMaintenancestateReportDataByDeviceIdByArrayItem(serviceCode, deviceId, modelCode, yearMonth, type, item);
        } else {
            if (StringUtils.isEmpty(item)) {
                jsonArray = reportService.GetMaintenancestateReportData(serviceCode, reportTempId, modelCode, type);
                ;
            } else if (item.startsWith("relation_")) {
                jsonArray = reportService.GetGetMaintenancestateReportDataByRelationArrayItem(serviceCode, reportTempId, modelCode, type, item);
            } else
                jsonArray = reportService.GetGetMaintenancestateReportDataByArrayItem(serviceCode, reportTempId, modelCode, type, item);
        }
        return gson.toJson(jsonArray);
    }

    @ApiOperation(value = "获取主机资产维护报表详细数据")
    @GetMapping(value = "/data/HOST")
    public Object GetHostMaintenancestateReport(@ApiParam(required = true, value = "报表临时表ID") @RequestParam(value = "reportTempId", required = false, defaultValue = "") String reportTempId,
                                                @ApiParam(required = true, value = "模型编号") @RequestParam(value = "modelCode", required = false, defaultValue = "") String modelCode,
                                                @ApiParam(required = true, value = "设备ID") @RequestParam(value = "deviceId", required = false, defaultValue = "") String deviceId,
                                                @ApiParam(required = true, value = "年月") @RequestParam(value = "month", required = false, defaultValue = "") String yearMonth,
                                                @ApiParam(required = true, value = "数据类别") @RequestParam(value = "type", required = false, defaultValue = "") String type,
                                                @ApiParam(required = true, value = "数据子集") @RequestParam(value = "item", required = false, defaultValue = "") String item,
                                                @ApiParam(required = true, value = "客服代号") @RequestParam(value = "serviceCode", required = false, defaultValue = "02100251") String serviceCode,
                                                @ApiParam(required = true, value = "eid") @RequestParam(value = "eid", required = false, defaultValue = "99990000") String eid) {
        if (StringUtils.isEmpty(reportTempId)) {
            return reportService.GetHostMaintenancestateReportByDeviceId(modelCode, deviceId, yearMonth, type, item, serviceCode, eid);
        } else {
            return reportService.GetHostMaintenancestateReport(reportTempId, modelCode, type, item, serviceCode, eid);
        }
    }

    @ApiOperation(value = "获取NAS资产维护报表详细数据")
    @GetMapping(value = "/data/NAS")
    public Object GetNasMaintenancestateReport(@ApiParam(required = true, value = "报表临时表ID") @RequestParam(value = "reportTempId", required = false, defaultValue = "") String reportTempId,
                                               @ApiParam(required = true, value = "模型编号") @RequestParam(value = "modelCode", required = false, defaultValue = "") String modelCode,
                                               @ApiParam(required = true, value = "设备ID") @RequestParam(value = "deviceId", required = false, defaultValue = "") String deviceId,
                                               @ApiParam(required = true, value = "年月") @RequestParam(value = "month", required = false, defaultValue = "") String yearMonth,
                                               @ApiParam(required = true, value = "数据类别") @RequestParam(value = "type", required = false, defaultValue = "") String type,
                                               @ApiParam(required = true, value = "数据子集") @RequestParam(value = "item", required = false, defaultValue = "") String item,
                                               @ApiParam(required = true, value = "客服代号") @RequestParam(value = "serviceCode", required = false, defaultValue = "02100251") String serviceCode,
                                               @ApiParam(required = true, value = "eid") @RequestParam(value = "eid", required = false, defaultValue = "99990000") String eid) {
        if (StringUtils.isEmpty(reportTempId)) {
            return reportService.GetNasMaintenancestateReportByDeviceId(modelCode, deviceId, yearMonth, type, item, serviceCode, eid);
        } else {
            return reportService.GetNasMaintenancestateReport(reportTempId, modelCode, type, item, serviceCode, eid);
        }
    }

    @ApiOperation(value = "获取Firewall资产维护报表详细数据")
    @GetMapping(value = "/data/Firewall")
    public Object GetFirewallMaintenancestateReport(@ApiParam(required = true, value = "报表临时表ID") @RequestParam(value = "reportTempId", required = false, defaultValue = "") String reportTempId,
                                                    @ApiParam(required = true, value = "模型编号") @RequestParam(value = "modelCode", required = false, defaultValue = "") String modelCode,
                                                    @ApiParam(required = true, value = "设备ID") @RequestParam(value = "deviceId", required = false, defaultValue = "") String deviceId,
                                                    @ApiParam(required = true, value = "年月") @RequestParam(value = "month", required = false, defaultValue = "") String yearMonth,
                                                    @ApiParam(required = true, value = "数据类别") @RequestParam(value = "type", required = false, defaultValue = "") String type,
                                                    @ApiParam(required = true, value = "数据子集") @RequestParam(value = "item", required = false, defaultValue = "") String item,
                                                    @ApiParam(required = true, value = "客服代号") @RequestParam(value = "serviceCode", required = false, defaultValue = "02100251") String serviceCode,
                                                    @ApiParam(required = true, value = "eid") @RequestParam(value = "eid", required = false, defaultValue = "99990000") String eid) {
        if (StringUtils.isEmpty(reportTempId)) {
            return reportService.GetFirewallMaintenancestateReportByDeviceId(modelCode, deviceId, yearMonth, type, item, serviceCode, eid);
        } else {
            return reportService.GetFirewallMaintenancestateReport(reportTempId, modelCode, type, item, serviceCode, eid);
        }
    }

    @ApiOperation(value = "获取MailServer资产维护报表详细数据")
    @GetMapping(value = "/data/MailServer")
    public Object GetMailServerMaintenancestateReport(@ApiParam(required = true, value = "报表临时表ID") @RequestParam(value = "reportTempId", required = false, defaultValue = "") String reportTempId,
                                                      @ApiParam(required = true, value = "模型编号") @RequestParam(value = "modelCode", required = false, defaultValue = "") String modelCode,
                                                      @ApiParam(required = true, value = "设备ID") @RequestParam(value = "deviceId", required = false, defaultValue = "") String deviceId,
                                                      @ApiParam(required = true, value = "年月") @RequestParam(value = "month", required = false, defaultValue = "") String yearMonth,
                                                      @ApiParam(required = true, value = "数据类别") @RequestParam(value = "type", required = false, defaultValue = "") String type,
                                                      @ApiParam(required = true, value = "数据子集") @RequestParam(value = "item", required = false, defaultValue = "") String item,
                                                      @ApiParam(required = true, value = "客服代号") @RequestParam(value = "serviceCode", required = false, defaultValue = "02100251") String serviceCode,
                                                      @ApiParam(required = true, value = "eid") @RequestParam(value = "eid", required = false, defaultValue = "99990000") String eid) {
        if (StringUtils.isEmpty(reportTempId)) {
            return reportService.GetMailServerMaintenancestateReportByDeviceId(modelCode, deviceId, yearMonth, type, item, serviceCode, eid);
        } else {
            return reportService.GetMailServerMaintenancestateReport(reportTempId, modelCode, type, item, serviceCode, eid);
        }
    }

    @ApiOperation(value = "获取Client资产维护报表详细数据")
    @GetMapping(value = "/data/Client")
    public Object GetClientMaintenancestateReport(@ApiParam(required = true, value = "报表临时表ID") @RequestParam(value = "reportTempId", required = false, defaultValue = "") String reportTempId,
                                                  @ApiParam(required = true, value = "模型编号") @RequestParam(value = "modelCode", required = false, defaultValue = "") String modelCode,
                                                  @ApiParam(required = true, value = "设备ID") @RequestParam(value = "deviceId", required = false, defaultValue = "") String deviceId,
                                                  @ApiParam(required = true, value = "年月") @RequestParam(value = "month", required = false, defaultValue = "") String yearMonth,
                                                  @ApiParam(required = true, value = "数据类别") @RequestParam(value = "type", required = false, defaultValue = "") String type,
                                                  @ApiParam(required = true, value = "数据子集") @RequestParam(value = "item", required = false, defaultValue = "") String item,
                                                  @ApiParam(required = true, value = "客服代号") @RequestParam(value = "serviceCode", required = false, defaultValue = "02100251") String serviceCode,
                                                  @ApiParam(required = true, value = "eid") @RequestParam(value = "eid", required = false, defaultValue = "99990000") String eid) {
        if (StringUtils.isEmpty(reportTempId)) {
            return reportService.GetClientMaintenancestateReportByDeviceId(modelCode, deviceId, yearMonth, type, item, serviceCode, eid);
        } else {
            return reportService.GetClientMaintenancestateReport(reportTempId, modelCode, type, item, serviceCode, eid);
        }
    }

    @ApiOperation(value = "获取BackupSchedule资产维护报表详细数据")
    @GetMapping(value = "/data/BackupSchedule")
    public Object GetBackupScheduleMaintenancestateReport(@ApiParam(required = true, value = "报表临时表ID") @RequestParam(value = "reportTempId", required = false, defaultValue = "") String reportTempId,
                                                          @ApiParam(required = true, value = "模型编号") @RequestParam(value = "modelCode", required = false, defaultValue = "") String modelCode,
                                                          @ApiParam(required = true, value = "设备ID") @RequestParam(value = "deviceId", required = false, defaultValue = "") String deviceId,
                                                          @ApiParam(required = true, value = "年月") @RequestParam(value = "month", required = false, defaultValue = "") String yearMonth,
                                                          @ApiParam(required = true, value = "数据类别") @RequestParam(value = "type", required = false, defaultValue = "") String type,
                                                          @ApiParam(required = true, value = "数据子集") @RequestParam(value = "item", required = false, defaultValue = "") String item,
                                                          @ApiParam(required = true, value = "客服代号") @RequestParam(value = "serviceCode", required = false, defaultValue = "02100251") String serviceCode,
                                                          @ApiParam(required = true, value = "eid") @RequestParam(value = "eid", required = false, defaultValue = "99990000") String eid) {
        /*if(StringUtils.isEmpty(reportTempId)) {
            return reportService.GetBackupScheduleMaintenancestateReportByDeviceId(modelCode, deviceId, yearMonth, type, item, serviceCode, eid);
        }else {
            return reportService.GetBackupScheduleMaintenancestateReport(reportTempId, modelCode, type, item, serviceCode, eid);
        }*/
        return reportService.GetBackupScheduleMaintenancestateReport(yearMonth, modelCode, type, item, serviceCode, eid);
    }

    @ApiOperation(value = "获取Software_Hardware资产维护报表详细数据")
    @GetMapping(value = "/data/Software_Hardware")
    public Object GetSoftware_HardwareMaintenancestateReport(@ApiParam(required = true, value = "报表临时表ID") @RequestParam(value = "reportTempId", required = false, defaultValue = "") String reportTempId,
                                                             @ApiParam(required = true, value = "模型编号") @RequestParam(value = "modelCode", required = false, defaultValue = "") String modelCode,
                                                             @ApiParam(required = true, value = "设备ID") @RequestParam(value = "deviceId", required = false, defaultValue = "") String deviceId,
                                                             @ApiParam(required = true, value = "年月") @RequestParam(value = "month", required = false, defaultValue = "") String yearMonth,
                                                             @ApiParam(required = true, value = "数据类别") @RequestParam(value = "type", required = false, defaultValue = "") String type,
                                                             @ApiParam(required = true, value = "数据子集") @RequestParam(value = "item", required = false, defaultValue = "") String item,
                                                             @ApiParam(required = true, value = "客服代号") @RequestParam(value = "serviceCode", required = false, defaultValue = "02100251") String serviceCode,
                                                             @ApiParam(required = true, value = "eid") @RequestParam(value = "eid", required = false, defaultValue = "99990000") String eid) {
        /*if(StringUtils.isEmpty(reportTempId)) {
            return reportService.GetSoftware_HardwareMaintenancestateReportByDeviceId(modelCode, deviceId, yearMonth, type, item, serviceCode, eid);
        }else {
            return reportService.GetSoftware_HardwareMaintenancestateReport(reportTempId, modelCode, type, item, serviceCode, eid);
        }*/
        System.out.println("reportTempId =" + reportTempId + ",modelCode=" + modelCode + ",item=" + item + ",eid=" + eid);
        return reportService.GetSoftware_HardwareMaintenancestateReport(reportTempId, modelCode, type, item, serviceCode, eid);

    }

    @ApiOperation(value = "获取Esxi资产维护报表详细数据")
    @GetMapping(value = "/data/Esxi")
    public Object GetEsxiMaintenancestateReport(@ApiParam(required = true, value = "报表临时表ID") @RequestParam(value = "reportTempId", required = false, defaultValue = "") String reportTempId,
                                                @ApiParam(required = true, value = "模型编号") @RequestParam(value = "modelCode", required = false, defaultValue = "") String modelCode,
                                                @ApiParam(required = true, value = "设备ID") @RequestParam(value = "deviceId", required = false, defaultValue = "") String deviceId,
                                                @ApiParam(required = true, value = "年月") @RequestParam(value = "month", required = false, defaultValue = "") String yearMonth,
                                                @ApiParam(required = true, value = "数据类别") @RequestParam(value = "type", required = false, defaultValue = "") String type,
                                                @ApiParam(required = true, value = "数据子集") @RequestParam(value = "item", required = false, defaultValue = "") String item,
                                                @ApiParam(required = true, value = "客服代号") @RequestParam(value = "serviceCode", required = false, defaultValue = "02100251") String serviceCode,
                                                @ApiParam(required = true, value = "eid") @RequestParam(value = "eid", required = false, defaultValue = "99990000") String eid) {
        if (StringUtils.isEmpty(reportTempId)) {
            return reportService.GetEsxiMaintenancestateReportByDeviceId(modelCode, deviceId, yearMonth, type, item, serviceCode, eid);
        } else {
            return reportService.GetEsxiMaintenancestateReport(reportTempId, modelCode, type, item, serviceCode, eid);
        }
    }

    @ApiOperation(value = "生成資產維護報表")
    @PostMapping(value = "/maintenance/save")
    public BaseResponse saveAssetMaintenanceReport(@RequestBody ReportMaintenance reportMaintenance) {
        return reportService.saveAssetMaintenanceReport(reportMaintenance);
    }

    @ApiOperation(value = "查詢資產維護報告設備")
    @GetMapping(value = "/maintenance/deviceList")
    public BaseResponse getMaintenanceDeviceList(@RequestParam(required = true) Long reportAssetMaintenanceId) {
        return reportService.getMaintenanceDeviceList(reportAssetMaintenanceId);
    }

    @ApiOperation(value = "新增資產維護報告閱覽紀錄")
    @PostMapping(value = "/maintenance/readLog")
    public BaseResponse createMaintenancereadLog(@RequestBody ReportMaintenanceReadLog reportMaintenanceReadLog) {
        return reportService.createMaintenancereadLog(reportMaintenanceReadLog);
    }

    @ApiOperation(value = "查詢資產維護報告閱覽紀錄")
    @GetMapping(value = "/maintenance/readLog")
    public BaseResponse getMaintenancereadLog(@RequestParam(required = true) Long reportAssetMaintenanceId,
                                              @RequestParam(required = true) Integer pageNum,
                                              @RequestParam(required = true) Integer pageSize) {
        return reportService.getMaintenancereadLog(reportAssetMaintenanceId, pageNum, pageSize);
    }

    @ApiOperation(value = "變更資產維護報告MIS端發送狀態")
    @PostMapping(value = "/maintenance/misStatus")
    public BaseResponse updateReportMISStatus(@RequestBody MISStatus misStatus) {
        return reportService.updateReportMISStatus(misStatus.getReportAssetMaintenanceId(), misStatus.getMisStatus(), misStatus.getSender());
    }

    @ApiOperation(value = "資產維護報告附件下載")
    @GetMapping("/{serviceCode}/file")
    public void downloadMaintenanceReportFile(@ApiParam(required = true, value = "客代") @PathVariable String serviceCode,
                                              @RequestParam(value = "reportId") String reportId,
                                              @RequestParam(value = "fileId") String fileId,
                                              HttpServletResponse response) {
        reportService.downloadMaintenanceReportFile(serviceCode, reportId, fileId, response);
    }

    @ApiOperation(value = "查询资产清册、软件清册列表")
    @GetMapping("/assetReportRecord")
    public BaseResponse getAssetReportRecord(@RequestParam(value = "eid", required = false) String eid,
                                             @RequestParam(value = "startReportDate", required = false) String startReportDate,
                                             @RequestParam(value = "endReportDate", required = false) String endReportDate,
                                             @RequestParam(value = "assetCategory", required = false) String assetCategory,
                                             @RequestParam(value = "userName", required = false) String userName,
                                             @RequestParam(value = "startReportGenerateTime", required = false) String startReportGenerateTime,
                                             @RequestParam(value = "endReportGenerateTime", required = false) String endReportGenerateTime,
                                             @RequestParam(value = "serviceCodeORCustomerName", required = false)  String serviceCodeORCustomerName) {
        return reportService.selectAssetReportRecord(eid, startReportDate, endReportDate, assetCategory, userName, startReportGenerateTime, endReportGenerateTime, serviceCodeORCustomerName);
    }

    @ApiOperation(value = "刪除资产清册、软件清册列表")
    @DeleteMapping("/deleteAssetReportRecord")
    public BaseResponse deleteAssetReportRecordById(@RequestParam(value = "id", required = true) Long id) {
        return reportService.deleteAssetReportRecordById(id);
    }

    @ApiOperation(value = "新增资产清册、软件清册列表")
    @PostMapping("/saveAssetReportRecord")
    public BaseResponse saveAssetReportRecord(@RequestBody AssetReportRecord assetReportRecord) {
        return reportService.insertAssetReportRecord(assetReportRecord);
    }

}
